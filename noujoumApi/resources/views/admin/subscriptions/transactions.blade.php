@extends('admin.layouts.app')

@section('title', 'Subscription Transactions')

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-receipt me-2"></i>
        Subscription Transactions
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ route('admin.subscriptions') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                Back to Dashboard
            </a>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Statuses</option>
                    <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                    <option value="approved" {{ request('status') === 'approved' ? 'selected' : '' }}>Approved</option>
                    <option value="rejected" {{ request('status') === 'rejected' ? 'selected' : '' }}>Rejected</option>
                    <option value="cancelled" {{ request('status') === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="user" class="form-label">User</label>
                <input type="text" class="form-control" id="user" name="user" value="{{ request('user') }}" placeholder="Search by user name">
            </div>
            <div class="col-md-3">
                <label for="date_from" class="form-label">From Date</label>
                <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request('date_from') }}">
            </div>
            <div class="col-md-3">
                <label for="date_to" class="form-label">To Date</label>
                <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request('date_to') }}">
            </div>
            <div class="col-12">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-1"></i>
                    Filter
                </button>
                <a href="{{ route('admin.subscriptions.transactions') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>
                    Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Transactions Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            All Transactions ({{ $transactions->total() }})
        </h5>
    </div>
    <div class="card-body">
        @if($transactions->count() > 0)
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Reference</th>
                            <th>User</th>
                            <th>Package</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Image</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($transactions as $transaction)
                        <tr>
                            <td>
                                <code>{{ $transaction->transaction_reference }}</code>
                            </td>
                            <td>
                                @if($transaction->user)
                                    <div>
                                        <strong>{{ $transaction->user->name }}</strong><br>
                                        <small class="text-muted">{{ $transaction->user->email }}</small>
                                    </div>
                                @else
                                    <span class="text-muted">N/A</span>
                                @endif
                            </td>
                            <td>
                                @if($transaction->package)
                                    <div>
                                        <strong>{{ $transaction->package->name }}</strong><br>
                                        <small class="text-muted">{{ $transaction->package->duration_months }} month(s)</small>
                                    </div>
                                @else
                                    <span class="text-muted">N/A</span>
                                @endif
                            </td>
                            <td>
                                <strong>{{ number_format($transaction->amount) }} {{ $transaction->currency }}</strong>
                            </td>
                            <td>
                                @if($transaction->status === 'pending')
                                    <span class="badge bg-warning">Pending</span>
                                @elseif($transaction->status === 'approved')
                                    <span class="badge bg-success">Approved</span>
                                @elseif($transaction->status === 'rejected')
                                    <span class="badge bg-danger">Rejected</span>
                                @elseif($transaction->status === 'cancelled')
                                    <span class="badge bg-secondary">Cancelled</span>
                                @else
                                    <span class="badge bg-secondary">{{ ucfirst($transaction->status) }}</span>
                                @endif
                            </td>
                            <td>
                                <div>
                                    <strong>{{ $transaction->created_at->format('M d, Y') }}</strong><br>
                                    <small class="text-muted">{{ $transaction->created_at->format('H:i') }}</small>
                                </div>
                            </td>
                            <td>
                                @if($transaction->transaction_image_url)
                                    @if($transaction->status === 'pending')
                                        <button class="btn btn-sm btn-primary" onclick="viewTransactionImageWithActions('{{ $transaction->transaction_image_url }}', '{{ $transaction->transaction_reference }}', {{ $transaction->id }}, '{{ $transaction->status }}')">
                                            <i class="fas fa-image"></i> Review
                                        </button>
                                    @else
                                        <button class="btn btn-sm btn-outline-primary" onclick="viewTransactionImage('{{ $transaction->transaction_image_url }}', '{{ $transaction->transaction_reference }}')">
                                            <i class="fas fa-image"></i> View
                                        </button>
                                    @endif
                                @else
                                    <span class="text-muted">No image</span>
                                @endif
                            </td>
                            <td>
                                @if($transaction->status === 'pending')
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-success" onclick="approveTransaction({{ $transaction->id }})">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn btn-danger" onclick="rejectTransaction({{ $transaction->id }})">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                @else
                                    <button class="btn btn-sm btn-outline-info" onclick="viewDetails({{ $transaction->id }})">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                @endif
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center">
                {{ $transactions->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No transactions found</h5>
                <p class="text-muted">No transactions match your current filters.</p>
            </div>
        @endif
    </div>
</div>

<!-- Transaction Image Modal -->
<div class="modal fade" id="transactionImageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-receipt me-2"></i>
                    Transaction Receipt - <span id="transactionRef"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <div class="mb-3" id="imageContainer">
                    <div id="imageLoader" class="d-none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2 text-muted">Loading transaction image...</p>
                    </div>
                    <img id="transactionImagePreview" src="" alt="Transaction Receipt"
                         class="img-fluid rounded shadow"
                         style="max-height: 500px; cursor: zoom-in;"
                         onclick="openImageInNewTab()"
                         onload="hideImageLoader()"
                         onerror="showImageError()">
                    <div id="imageError" class="d-none alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Unable to load transaction image. The image may have been moved or deleted.
                    </div>
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Verify the payment details:</strong> Check the amount, date, and account information match the transaction.
                    <br><small class="text-muted">Click on the image to open it in a new tab for better viewing.</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <div class="btn-group" id="transactionActions" style="display: none;">
                    <button type="button" class="btn btn-success" onclick="approveFromModal()">
                        <i class="fas fa-check me-1"></i>
                        Approve Transaction
                    </button>
                    <button type="button" class="btn btn-danger" onclick="rejectFromModal()">
                        <i class="fas fa-times me-1"></i>
                        Reject Transaction
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    const API_BASE = '/api/admin';
    let currentTransactionId = null;

    function viewTransactionImage(imageUrl, transactionRef) {
        showImageLoader();
        document.getElementById('transactionImagePreview').src = imageUrl;
        document.getElementById('transactionRef').textContent = transactionRef;

        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('transactionImageModal'));
        modal.show();

        // Hide action buttons for non-pending transactions
        document.getElementById('transactionActions').style.display = 'none';
    }

    function viewTransactionImageWithActions(imageUrl, transactionRef, transactionId, status) {
        showImageLoader();
        document.getElementById('transactionImagePreview').src = imageUrl;
        document.getElementById('transactionRef').textContent = transactionRef;
        currentTransactionId = transactionId;

        // Show action buttons only if transaction is pending
        if (status === 'pending') {
            document.getElementById('transactionActions').style.display = 'block';
        } else {
            document.getElementById('transactionActions').style.display = 'none';
        }

        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('transactionImageModal'));
        modal.show();
    }

    async function approveTransaction(id) {
        if (!confirm('Are you sure you want to approve this transaction?')) return;
        
        try {
            const response = await fetch(`${API_BASE}/subscription/transactions/${id}/approve`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });
            
            const data = await response.json();
            
            if (data.success) {
                alert('Transaction approved successfully!');
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        } catch (error) {
            alert('Error approving transaction: ' + error.message);
        }
    }
    
    async function rejectTransaction(id) {
        const reason = prompt('Reason for rejection (optional):');
        if (reason === null) return; // User cancelled
        
        try {
            const response = await fetch(`${API_BASE}/subscription/transactions/${id}/reject`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ reason })
            });
            
            const data = await response.json();
            
            if (data.success) {
                alert('Transaction rejected successfully!');
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        } catch (error) {
            alert('Error rejecting transaction: ' + error.message);
        }
    }

    function viewDetails(id) {
        // For now, just show an alert
        // You can implement a detailed view modal
        alert('View details functionality - to be implemented');
    }

    // Modal action functions
    function approveFromModal() {
        if (currentTransactionId) {
            // Close modal first
            bootstrap.Modal.getInstance(document.getElementById('transactionImageModal')).hide();
            // Then approve
            approveTransaction(currentTransactionId);
        }
    }

    function rejectFromModal() {
        if (currentTransactionId) {
            // Close modal first
            bootstrap.Modal.getInstance(document.getElementById('transactionImageModal')).hide();
            // Then reject
            rejectTransaction(currentTransactionId);
        }
    }

    // Image loading helper functions
    function showImageLoader() {
        document.getElementById('imageLoader').classList.remove('d-none');
        document.getElementById('transactionImagePreview').classList.add('d-none');
        document.getElementById('imageError').classList.add('d-none');
    }

    function hideImageLoader() {
        document.getElementById('imageLoader').classList.add('d-none');
        document.getElementById('transactionImagePreview').classList.remove('d-none');
        document.getElementById('imageError').classList.add('d-none');
    }

    function showImageError() {
        document.getElementById('imageLoader').classList.add('d-none');
        document.getElementById('transactionImagePreview').classList.add('d-none');
        document.getElementById('imageError').classList.remove('d-none');
    }

    function openImageInNewTab() {
        const imageUrl = document.getElementById('transactionImagePreview').src;
        if (imageUrl) {
            window.open(imageUrl, '_blank');
        }
    }
</script>
@endsection
