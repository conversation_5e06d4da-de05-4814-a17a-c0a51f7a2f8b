@extends('admin.layouts.app')

@section('title', 'Subscription Packages')

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-box me-2"></i>
        Subscription Packages
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ route('admin.subscriptions') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                Back to Dashboard
            </a>
            <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#createPackageModal">
                <i class="fas fa-plus me-1"></i>
                New Package
            </button>
        </div>
    </div>
</div>

<!-- Packages List -->
<div class="row">
    @forelse($packages as $package)
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ $package->name }}</h5>
                @if($package->badge)
                    <span class="badge bg-warning">{{ $package->badge }}</span>
                @endif
            </div>
            <div class="card-body">
                @if($package->description)
                    <p class="card-text text-muted">{{ $package->description }}</p>
                @endif
                
                <div class="mb-3">
                    <div class="d-flex align-items-center">
                        @if($package->original_price && $package->original_price > $package->price)
                            <span class="text-decoration-line-through text-muted me-2">
                                {{ number_format($package->original_price) }} MRU
                            </span>
                        @endif
                        <h4 class="text-primary mb-0">{{ number_format($package->price) }} MRU</h4>
                    </div>
                    <small class="text-muted">{{ $package->duration_months }} month(s)</small>
                </div>

                @if($package->features)
                    <ul class="list-unstyled">
                        @foreach($package->features as $feature)
                            <li><i class="fas fa-check text-success me-2"></i>{{ $feature }}</li>
                        @endforeach
                    </ul>
                @endif

                <div class="mt-3">
                    <small class="text-muted">
                        Status: 
                        @if($package->is_active)
                            <span class="badge bg-success">Active</span>
                        @else
                            <span class="badge bg-secondary">Inactive</span>
                        @endif
                    </small>
                </div>
            </div>
            <div class="card-footer">
                <div class="btn-group w-100">
                    <button class="btn btn-outline-primary btn-sm" onclick="editPackage({{ $package->id }})">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="deletePackage({{ $package->id }})">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        </div>
    </div>
    @empty
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-box fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No packages found</h5>
                <p class="text-muted">Create your first subscription package to get started.</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createPackageModal">
                    <i class="fas fa-plus me-1"></i>
                    Create Package
                </button>
            </div>
        </div>
    </div>
    @endforelse
</div>

<!-- Create Package Modal -->
<div class="modal fade" id="createPackageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Package</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createPackageForm">
                    <div class="mb-3">
                        <label for="name" class="form-label">Package Name</label>
                        <input type="text" class="form-control" id="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" rows="3"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="duration_months" class="form-label">Duration (months)</label>
                                <input type="number" class="form-control" id="duration_months" min="1" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="price" class="form-label">Price (MRU)</label>
                                <input type="number" class="form-control" id="price" min="0" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="original_price" class="form-label">Original Price (optional)</label>
                        <input type="number" class="form-control" id="original_price" min="0">
                        <small class="form-text text-muted">Leave empty if no discount</small>
                    </div>
                    <div class="mb-3">
                        <label for="badge" class="form-label">Badge (optional)</label>
                        <input type="text" class="form-control" id="badge" placeholder="e.g., Popular, Best Value">
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" checked>
                            <label class="form-check-label" for="is_active">
                                Active
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="createPackage()">Create Package</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    const API_BASE = '/api/admin';

    async function createPackage() {
        const form = document.getElementById('createPackageForm');
        const formData = new FormData(form);
        
        const data = {
            name: document.getElementById('name').value,
            description: document.getElementById('description').value,
            duration_months: parseInt(document.getElementById('duration_months').value),
            price: parseFloat(document.getElementById('price').value),
            original_price: document.getElementById('original_price').value ? parseFloat(document.getElementById('original_price').value) : null,
            badge: document.getElementById('badge').value || null,
            is_active: document.getElementById('is_active').checked
        };

        try {
            const response = await fetch(`${API_BASE}/subscription/packages`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                alert('Package created successfully!');
                location.reload();
            } else {
                alert('Error: ' + result.message);
            }
        } catch (error) {
            alert('Error creating package: ' + error.message);
        }
    }

    async function editPackage(id) {
        // For now, just redirect to a simple edit form
        // You can implement a modal edit form similar to create
        alert('Edit functionality - to be implemented');
    }

    async function deletePackage(id) {
        if (!confirm('Are you sure you want to delete this package?')) return;

        try {
            const response = await fetch(`${API_BASE}/subscription/packages/${id}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const result = await response.json();

            if (result.success) {
                alert('Package deleted successfully!');
                location.reload();
            } else {
                alert('Error: ' + result.message);
            }
        } catch (error) {
            alert('Error deleting package: ' + error.message);
        }
    }
</script>
@endsection
