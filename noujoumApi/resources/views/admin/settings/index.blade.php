@extends('admin.layouts.app')

@section('title', 'System Settings')

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-cog me-2"></i>
        System Settings
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ route('admin.subscriptions') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                Back to Dashboard
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Free Trial Settings -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-gift me-2"></i>
                    Free Trial Settings
                </h5>
            </div>
            <div class="card-body">
                <form id="freeTrialForm">
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="free_trial_enabled" name="free_trial_enabled"
                                   {{ ($settings['free_trial_enabled']->value ?? false) ? 'checked' : '' }}>
                            <label class="form-check-label" for="free_trial_enabled">
                                Enable Free Trial
                            </label>
                            <small class="form-text text-muted d-block">Enable global free trial for all users</small>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="free_trial_start_date" class="form-label">Free Trial Start Date</label>
                        <input type="date" class="form-control" id="free_trial_start_date" name="free_trial_start_date"
                               value="{{ $settings['free_trial_start_date']->value ?? now()->format('Y-m-d') }}">
                        <small class="form-text text-muted">When the free trial period begins</small>
                    </div>

                    <div class="mb-3">
                        <label for="free_trial_end_date" class="form-label">Free Trial End Date</label>
                        <input type="date" class="form-control" id="free_trial_end_date" name="free_trial_end_date"
                               value="{{ $settings['free_trial_end_date']->value ?? now()->addMonths(6)->format('Y-m-d') }}">
                        <small class="form-text text-muted">When the free trial period ends</small>
                    </div>

                    <button type="button" class="btn btn-primary" onclick="saveFreeTrialSettings()">
                        <i class="fas fa-save me-1"></i>
                        Save Free Trial Settings
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Payment Information -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-credit-card me-2"></i>
                    Payment Information
                </h5>
            </div>
            <div class="card-body">
                <form id="paymentForm">
                    <div class="mb-3">
                        <label for="bank_name" class="form-label">Bank Name</label>
                        <input type="text" class="form-control" id="bank_name" name="payment_bank_name"
                               value="{{ $settings['payment_bank_name']->value ?? 'Banque Mauritanienne' }}">
                    </div>

                    <div class="mb-3">
                        <label for="account_number" class="form-label">Account Number</label>
                        <input type="text" class="form-control" id="account_number" name="payment_account_number"
                               value="{{ $settings['payment_account_number']->value ?? '' }}">
                    </div>

                    <div class="mb-3">
                        <label for="account_name" class="form-label">Account Name</label>
                        <input type="text" class="form-control" id="account_name" name="payment_account_name"
                               value="{{ $settings['payment_account_name']->value ?? 'Noujoum Store' }}">
                    </div>

                    <div class="mb-3">
                        <label for="phone_number" class="form-label">Phone Number</label>
                        <input type="text" class="form-control" id="phone_number" name="payment_phone_number"
                               value="{{ $settings['payment_phone_number']->value ?? '+222 XX XX XX XX' }}">
                        <small class="form-text text-muted">Phone number for mobile money transfers</small>
                    </div>

                    <div class="mb-3">
                        <label for="payment_instructions" class="form-label">Payment Instructions</label>
                        <textarea class="form-control" id="payment_instructions" name="payment_instructions" rows="4">{{ $settings['payment_instructions']->value ?? 'Please transfer the amount to the account details above and upload the receipt.' }}</textarea>
                    </div>

                    <button type="button" class="btn btn-primary" onclick="savePaymentSettings()">
                        <i class="fas fa-save me-1"></i>
                        Save Payment Settings
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    async function saveFreeTrialSettings() {
        const settings = {
            free_trial_duration_days: parseInt(document.getElementById('free_trial_duration').value),
            free_trial_start_date: document.getElementById('free_trial_start_date').value,
            subscription_required_for_publishing: document.getElementById('subscription_required').checked
        };

        try {
            const response = await fetch('{{ route("admin.settings.bulk-update") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ settings })
            });

            const data = await response.json();

            if (data.success) {
                alert('Free trial settings saved successfully!');
            } else {
                alert('Error: ' + data.message);
            }
        } catch (error) {
            alert('Error saving settings: ' + error.message);
        }
    }

    async function savePaymentSettings() {
        const settings = {
            payment_bank_name: document.getElementById('bank_name').value,
            payment_account_number: document.getElementById('account_number').value,
            payment_account_name: document.getElementById('account_name').value,
            payment_phone_number: document.getElementById('phone_number').value,
            payment_instructions: document.getElementById('payment_instructions').value
        };

        try {
            const response = await fetch('{{ route("admin.settings.bulk-update") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ settings })
            });

            const data = await response.json();

            if (data.success) {
                alert('Payment settings saved successfully!');
            } else {
                alert('Error: ' + data.message);
            }
        } catch (error) {
            alert('Error saving settings: ' + error.message);
        }
    }
</script>
@endsection