<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasApiTokens;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'company_name',
        'website',
        'bio',
        'avatar_url',
        'role',
        'is_verified',
        'last_login_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_verified' => 'boolean',
            'last_login_at' => 'datetime',
        ];
    }

    /**
     * Get the apps for the user.
     */
    public function apps(): HasMany
    {
        return $this->hasMany(App::class);
    }

    /**
     * Get the user's subscriptions.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(UserSubscription::class);
    }

    /**
     * Get the user's current active subscription.
     */
    public function currentSubscription()
    {
        return $this->subscriptions()
            ->where(function ($query) {
                $query->where('status', 'active')
                      ->orWhere('status', 'free_trial');
            })
            ->where('expires_at', '>', now())
            ->orderBy('expires_at', 'desc')
            ->first();
    }

    /**
     * Get the user's payment transactions.
     */
    public function paymentTransactions(): HasMany
    {
        return $this->hasMany(PaymentTransaction::class);
    }

    /**
     * Check if user is admin.
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Scope a query to only include verified users.
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Check if user can publish apps (global free trial OR active subscription).
     */
    public function canPublishApps(): bool
    {
        // PRIORITY 1: Global free trial - if active, ANYONE can publish
        if ($this->isGlobalFreeTrialActive()) {
            return true;
        }

        // PRIORITY 2: Individual subscription - check if user has active subscription
        $subscription = $this->currentSubscription();
        if (!$subscription) {
            return false;
        }

        // User has subscription - check if it's active (paid) or individual free trial
        return $subscription->isActive() || $subscription->isFreeTrial();
    }

    /**
     * Check if user is in free trial period (global or individual).
     */
    public function isInFreeTrial(): bool
    {
        // Check global free trial first - this takes priority
        if ($this->isGlobalFreeTrialActive()) {
            return true;
        }

        // Then check individual subscription free trial
        $subscription = $this->currentSubscription();
        return $subscription && $subscription->isFreeTrial();
    }

    /**
     * Check if global free trial is currently active.
     */
    public function isGlobalFreeTrialActive(): bool
    {
        $freeTrialEnabled = \App\Models\AppSetting::get('free_trial_enabled', false);
        $startDate = \App\Models\AppSetting::get('free_trial_start_date');
        $endDate = \App\Models\AppSetting::get('free_trial_end_date');

        if (!$freeTrialEnabled || !$startDate || !$endDate) {
            return false;
        }

        $start = \Carbon\Carbon::parse($startDate);
        $end = \Carbon\Carbon::parse($endDate);
        $now = now();

        return $now->between($start, $end);
    }

    /**
     * Get subscription status for display.
     */
    public function getSubscriptionStatusAttribute(): string
    {
        $subscription = $this->currentSubscription();

        if (!$subscription) {
            return 'Aucun abonnement';
        }

        if ($subscription->isFreeTrial()) {
            return 'Essai gratuit';
        }

        if ($subscription->isActive()) {
            return 'Abonnement actif';
        }

        return 'Abonnement expiré';
    }
}
