<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;
use App\Models\AppSetting;

class UserSubscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'package_id',
        'status',
        'starts_at',
        'expires_at',
        'free_trial_ends_at',
        'is_free_trial',
        'amount_paid',
        'currency',
        'notes',
    ];

    protected $casts = [
        'starts_at' => 'datetime',
        'expires_at' => 'datetime',
        'free_trial_ends_at' => 'datetime',
        'is_free_trial' => 'boolean',
        'amount_paid' => 'decimal:2',
    ];

    /**
     * Get the user that owns the subscription.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the subscription package.
     */
    public function package(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPackage::class, 'package_id');
    }

    /**
     * Get the payment transactions for this subscription.
     */
    public function paymentTransactions(): HasMany
    {
        return $this->hasMany(PaymentTransaction::class, 'subscription_id');
    }

    /**
     * Scope a query to only include active subscriptions.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where('expires_at', '>', now());
    }

    /**
     * Scope a query to only include free trial subscriptions.
     */
    public function scopeFreeTrial($query)
    {
        return $query->where('is_free_trial', true)
                    ->where('status', 'free_trial');
    }

    /**
     * Scope a query to only include expired subscriptions.
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<=', now())
                    ->whereIn('status', ['active', 'free_trial']);
    }

    /**
     * Check if the subscription is currently active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active' && $this->expires_at > now();
    }

    /**
     * Check if the subscription is in free trial.
     */
    public function isFreeTrial(): bool
    {
        // First check global free trial settings
        $freeTrialEnabled = AppSetting::get('free_trial_enabled', false);
        $freeTrialStartDate = AppSetting::get('free_trial_start_date');
        $freeTrialEndDate = AppSetting::get('free_trial_end_date');

        // Check if global free trial is active
        if ($freeTrialEnabled && $freeTrialStartDate && $freeTrialEndDate) {
            $start = Carbon::parse($freeTrialStartDate);
            $end = Carbon::parse($freeTrialEndDate);
            $now = now();

            // If global free trial is active, return true
            if ($now->between($start, $end)) {
                return true;
            }
        }

        // Then check individual subscription free trial
        return $this->is_free_trial && $this->expires_at > now();
    }

    /**
     * Check if the subscription is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at <= now();
    }

    /**
     * Get days remaining in subscription.
     */
    public function getDaysRemainingAttribute(): int
    {
        if ($this->isExpired()) {
            return 0;
        }

        return $this->expires_at->diffInDays(now());
    }

    /**
     * Get the status label for display.
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'free_trial' => 'Essai gratuit',
            'active' => 'Actif',
            'expired' => 'Expiré',
            'cancelled' => 'Annulé',
            default => 'Inconnu'
        };
    }

    /**
     * Extend the subscription by adding months.
     */
    public function extend(int $months): void
    {
        $this->expires_at = $this->expires_at->addMonths($months);
        $this->save();
    }

    /**
     * Activate the subscription with a package.
     */
    public function activate(SubscriptionPackage $package, float $amountPaid = null): void
    {
        $this->package_id = $package->id;
        $this->status = 'active';
        $this->is_free_trial = false;
        $this->amount_paid = $amountPaid ?? $package->price;
        $this->expires_at = now()->addMonths($package->duration_months);
        $this->save();
    }
}
