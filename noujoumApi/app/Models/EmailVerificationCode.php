<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class EmailVerificationCode extends Model
{
    protected $fillable = [
        'email',
        'code',
        'expires_at',
        'is_used'
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'is_used' => 'boolean'
    ];

    /**
     * Generate a new verification code for an email
     */
    public static function generateCode(string $email): string
    {
        // Delete any existing codes for this email
        self::where('email', $email)->delete();

        // Generate a 6-digit code
        $code = str_pad(random_int(100000, 999999), 6, '0', STR_PAD_LEFT);

        // Create new verification code (expires in 15 minutes)
        self::create([
            'email' => $email,
            'code' => $code,
            'expires_at' => Carbon::now()->addMinutes(15),
            'is_used' => false
        ]);

        return $code;
    }

    /**
     * Verify a code for an email
     */
    public static function verifyCode(string $email, string $code): bool
    {
        $verificationCode = self::where('email', $email)
            ->where('code', $code)
            ->where('is_used', false)
            ->where('expires_at', '>', Carbon::now())
            ->first();

        if ($verificationCode) {
            $verificationCode->update(['is_used' => true]);
            return true;
        }

        return false;
    }

    /**
     * Clean up expired codes
     */
    public static function cleanupExpired(): void
    {
        self::where('expires_at', '<', Carbon::now())->delete();
    }
}
