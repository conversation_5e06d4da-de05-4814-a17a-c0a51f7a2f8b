<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\EmailVerificationCode;

class CleanupExpiredCodesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:cleanup-expired';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up expired email verification codes';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Cleaning up expired verification codes...');

        EmailVerificationCode::cleanupExpired();

        $this->info('✅ Expired verification codes cleaned up successfully!');
    }
}
