<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use App\Mail\EmailVerificationMail;

class TestEmailCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test email configuration by sending a verification email';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');

        try {
            $this->info('Sending test email to: ' . $email);

            Mail::to($email)->send(new EmailVerificationMail('123456', 'Test User'));

            $this->info('✅ Email sent successfully!');
            $this->info('Check your inbox and spam folder.');

        } catch (\Exception $e) {
            $this->error('❌ Failed to send email: ' . $e->getMessage());
            $this->error('Please check your email configuration in .env file');
        }
    }
}
