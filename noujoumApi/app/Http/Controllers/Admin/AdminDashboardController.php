<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\App;
use App\Models\Category;
use App\Models\SubscriptionPackage;
use App\Models\UserSubscription;
use App\Models\PaymentTransaction;
use App\Models\AppSetting;
use Illuminate\Support\Facades\DB;

class AdminDashboardController extends Controller
{
    public function __construct()
    {
        // $this->middleware(function ($request, $next) {
        //     if (!auth()->user()->is_admin) {
        //         abort(403, 'Access denied. Admin privileges required.');
        //     }
        //     return $next($request);
        // });
    }

    public function index()
    {
        $stats = [
            'total_users' => User::count(),
            'total_apps' => App::count(),
            'pending_apps' => App::where('is_approved', false)->count(),
            'approved_apps' => App::where('is_approved', true)->count(),
            'verified_users' => User::where('is_verified', true)->count(),
            'total_categories' => Category::count(),
        ];

        $recent_apps = App::with(['user', 'category'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        $recent_users = User::orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return view('admin.dashboard', compact('stats', 'recent_apps', 'recent_users'));
    }

    public function users()
    {
        $users = User::with('apps')
            ->withCount('apps')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('admin.users.index', compact('users'));
    }

    public function showUser($id)
    {
        $user = User::with(['apps' => function($query) {
            $query->orderBy('created_at', 'desc');
        }])->findOrFail($id);

        return view('admin.users.show', compact('user'));
    }

    public function verifyUser($id)
    {
        $user = User::findOrFail($id);
        $user->update(['is_verified' => true]);

        return back()->with('success', 'User verified successfully.');
    }

    public function unverifyUser($id)
    {
        $user = User::findOrFail($id);
        $user->update(['is_verified' => false]);

        return back()->with('success', 'User verification removed.');
    }

    public function apps()
    {
        $apps = App::with(['user', 'category'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('admin.apps.index', compact('apps'));
    }

    public function showApp($id)
    {
        $app = App::with(['user', 'category'])->findOrFail($id);
        return view('admin.apps.show', compact('app'));
    }

    public function approveApp($id)
    {
        $app = App::findOrFail($id);
        $app->update(['is_approved' => true]);

        return back()->with('success', 'App approved successfully.');
    }

    public function rejectApp($id)
    {
        $app = App::findOrFail($id);
        $app->update(['is_approved' => false]);

        return back()->with('success', 'App rejected.');
    }

    public function featureApp($id)
    {
        $app = App::findOrFail($id);
        $app->update(['is_featured' => true]);

        return back()->with('success', 'App featured successfully.');
    }

    public function unfeatureApp($id)
    {
        $app = App::findOrFail($id);
        $app->update(['is_featured' => false]);

        return back()->with('success', 'App unfeatured.');
    }

    public function deleteApp($id)
    {
        $app = App::findOrFail($id);
        $app->delete();

        return back()->with('success', 'App deleted successfully.');
    }

    // Subscription Management Methods
    public function subscriptions()
    {
        $stats = [
            'total_users' => User::count(),
            'active_subscriptions' => UserSubscription::where('status', 'active')->count(),
            'free_trial_users' => UserSubscription::where('is_free_trial', true)->count(),
            'pending_payments' => PaymentTransaction::where('status', 'pending')->count(),
            'total_revenue' => PaymentTransaction::where('status', 'approved')->sum('amount'),
        ];

        $recent_transactions = PaymentTransaction::with(['user', 'package'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return view('admin.subscriptions.index', compact('stats', 'recent_transactions'));
    }

    public function subscriptionPackages()
    {
        $packages = SubscriptionPackage::orderBy('sort_order')->get();
        return view('admin.subscriptions.packages', compact('packages'));
    }

    public function subscriptionTransactions()
    {
        $transactions = PaymentTransaction::with(['user', 'package', 'subscription'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('admin.subscriptions.transactions', compact('transactions'));
    }

    public function settings()
    {
        $settings = AppSetting::all()->keyBy('key');
        return view('admin.settings.index', compact('settings'));
    }

    public function updateSettings(Request $request)
    {
        try {
            $settings = $request->input('settings', []);
            $updatedSettings = [];

            foreach ($settings as $key => $value) {
                // Find or create the setting
                $setting = AppSetting::where('key', $key)->first();

                if (!$setting) {
                    // Create new setting if it doesn't exist
                    $setting = new AppSetting();
                    $setting->key = $key;
                    $setting->group = $this->getSettingGroup($key);
                    $setting->label = $this->getSettingLabel($key);
                    $setting->type = $this->getSettingType($key, $value);
                    $setting->is_public = $this->isPublicSetting($key);
                }

                $setting->setTypedValue($value);
                $setting->save();
                $updatedSettings[] = $setting;
            }

            // Clear cache after updating settings
            AppSetting::clearCache();

            return response()->json([
                'success' => true,
                'message' => count($updatedSettings) . ' paramètres mis à jour avec succès',
                'data' => $updatedSettings
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la mise à jour des paramètres',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get the appropriate group for a setting key.
     */
    private function getSettingGroup(string $key): string
    {
        if (str_starts_with($key, 'free_trial_')) {
            return 'free_trial';
        }
        if (str_starts_with($key, 'payment_')) {
            return 'payment';
        }
        if (str_starts_with($key, 'subscription_')) {
            return 'subscription';
        }
        return 'general';
    }

    /**
     * Get the appropriate label for a setting key.
     */
    private function getSettingLabel(string $key): string
    {
        $labels = [
            'free_trial_enabled' => 'Essai gratuit activé',
            'free_trial_start_date' => 'Date de début de l\'essai gratuit',
            'free_trial_end_date' => 'Date de fin de l\'essai gratuit',
            'payment_bank_name' => 'Nom de la banque',
            'payment_account_number' => 'Numéro de compte',
            'payment_account_name' => 'Nom du compte',
            'payment_phone_number' => 'Numéro de téléphone',
            'payment_instructions' => 'Instructions de paiement',
        ];

        return $labels[$key] ?? ucfirst(str_replace('_', ' ', $key));
    }

    /**
     * Get the appropriate type for a setting based on key and value.
     */
    private function getSettingType(string $key, $value): string
    {
        if (is_bool($value) || $key === 'free_trial_enabled') {
            return 'boolean';
        }
        if ($key === 'free_trial_start_date' || $key === 'free_trial_end_date') {
            return 'date';
        }
        return 'string';
    }

    /**
     * Check if a setting should be public.
     */
    private function isPublicSetting(string $key): bool
    {
        $publicSettings = [
            'payment_bank_name',
            'payment_account_number',
            'payment_account_name',
            'payment_phone_number',
            'payment_instructions',
        ];

        return in_array($key, $publicSettings);
    }
}
