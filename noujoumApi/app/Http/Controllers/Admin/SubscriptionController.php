<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SubscriptionPackage;
use App\Models\UserSubscription;
use App\Models\PaymentTransaction;
use App\Models\AppSetting;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class SubscriptionController extends Controller
{
    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            if (!$request->user()->isAdmin()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Admin access required'
                ], 403);
            }
            return $next($request);
        });
    }

    /**
     * Get subscription statistics.
     */
    public function stats(): JsonResponse
    {
        $stats = [
            'total_subscriptions' => UserSubscription::count(),
            'active_subscriptions' => UserSubscription::active()->count(),
            'free_trial_subscriptions' => UserSubscription::freeTrial()->count(),
            'expired_subscriptions' => UserSubscription::expired()->count(),
            'pending_payments' => PaymentTransaction::pending()->count(),
            'total_revenue' => PaymentTransaction::approved()->sum('amount'),
            'monthly_revenue' => PaymentTransaction::approved()
                ->whereMonth('created_at', now()->month)
                ->sum('amount'),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Get all subscription packages.
     */
    public function packages(): JsonResponse
    {
        $packages = SubscriptionPackage::orderBy('sort_order')
            ->orderBy('duration_months')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $packages
        ]);
    }

    /**
     * Create a new subscription package.
     */
    public function createPackage(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'duration_months' => 'required|integer|min:1',
            'price' => 'required|numeric|min:0',
            'original_price' => 'nullable|numeric|min:0',
            'is_active' => 'boolean',
            'sort_order' => 'integer',
            'features' => 'nullable|array',
            'badge' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $package = SubscriptionPackage::create($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Package créé avec succès',
            'data' => $package
        ], 201);
    }

    /**
     * Update a subscription package.
     */
    public function updatePackage(Request $request, $id): JsonResponse
    {
        $package = SubscriptionPackage::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'duration_months' => 'required|integer|min:1',
            'price' => 'required|numeric|min:0',
            'original_price' => 'nullable|numeric|min:0',
            'is_active' => 'boolean',
            'sort_order' => 'integer',
            'features' => 'nullable|array',
            'badge' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $package->update($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Package mis à jour avec succès',
            'data' => $package
        ]);
    }

    /**
     * Delete a subscription package.
     */
    public function deletePackage($id): JsonResponse
    {
        $package = SubscriptionPackage::findOrFail($id);
        
        // Check if package has active subscriptions
        if ($package->userSubscriptions()->active()->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Impossible de supprimer un package avec des abonnements actifs'
            ], 400);
        }

        $package->delete();

        return response()->json([
            'success' => true,
            'message' => 'Package supprimé avec succès'
        ]);
    }

    /**
     * Get all payment transactions.
     */
    public function transactions(Request $request): JsonResponse
    {
        $query = PaymentTransaction::with(['user', 'package', 'reviewer']);

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            })->orWhere('transaction_reference', 'like', "%{$search}%");
        }

        $transactions = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 20));

        return response()->json([
            'success' => true,
            'data' => $transactions
        ]);
    }

    /**
     * Approve a payment transaction.
     */
    public function approveTransaction(Request $request, $id): JsonResponse
    {
        $transaction = PaymentTransaction::findOrFail($id);

        if (!$transaction->isPending()) {
            return response()->json([
                'success' => false,
                'message' => 'Cette transaction ne peut pas être approuvée'
            ], 400);
        }

        $validator = Validator::make($request->all(), [
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Approve the transaction
            $transaction->approve($request->user(), $request->admin_notes);

            // Create or extend user subscription
            $user = $transaction->user;
            $package = $transaction->package;
            
            $currentSubscription = $user->currentSubscription();
            
            if ($currentSubscription && $currentSubscription->isActive()) {
                // Extend existing subscription
                $currentSubscription->extend($package->duration_months);
                $transaction->update(['subscription_id' => $currentSubscription->id]);
            } else {
                // Create new subscription
                $subscription = UserSubscription::create([
                    'user_id' => $user->id,
                    'package_id' => $package->id,
                    'status' => 'active',
                    'starts_at' => now(),
                    'expires_at' => now()->addMonths($package->duration_months),
                    'is_free_trial' => false,
                    'amount_paid' => $transaction->amount,
                    'currency' => $transaction->currency,
                    'notes' => 'Abonnement activé suite au paiement ' . $transaction->transaction_reference,
                ]);
                
                $transaction->update(['subscription_id' => $subscription->id]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Transaction approuvée et abonnement activé',
                'data' => $transaction->fresh(['user', 'package', 'reviewer', 'subscription'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de l\'approbation de la transaction',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reject a payment transaction.
     */
    public function rejectTransaction(Request $request, $id): JsonResponse
    {
        $transaction = PaymentTransaction::findOrFail($id);

        if (!$transaction->isPending()) {
            return response()->json([
                'success' => false,
                'message' => 'Cette transaction ne peut pas être rejetée'
            ], 400);
        }

        $validator = Validator::make($request->all(), [
            'admin_notes' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $transaction->reject($request->user(), $request->admin_notes);

        return response()->json([
            'success' => true,
            'message' => 'Transaction rejetée',
            'data' => $transaction->fresh(['user', 'package', 'reviewer'])
        ]);
    }
}
