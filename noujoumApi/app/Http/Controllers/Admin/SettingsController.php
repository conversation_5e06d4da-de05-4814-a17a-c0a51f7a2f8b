<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AppSetting;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class SettingsController extends Controller
{
    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            if (!$request->user()->isAdmin()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Admin access required'
                ], 403);
            }
            return $next($request);
        });
    }

    /**
     * Get all settings grouped by category.
     */
    public function index(): JsonResponse
    {
        $settings = AppSetting::orderBy('group')
            ->orderBy('key')
            ->get()
            ->groupBy('group');

        return response()->json([
            'success' => true,
            'data' => $settings
        ]);
    }

    /**
     * Get settings by group.
     */
    public function byGroup(string $group): JsonResponse
    {
        $settings = AppSetting::byGroup($group)
            ->orderBy('key')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $settings
        ]);
    }

    /**
     * Update a setting.
     */
    public function update(Request $request, $id): JsonResponse
    {
        $setting = AppSetting::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'value' => 'required',
            'label' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'is_public' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        // Set the typed value
        $setting->setTypedValue($request->value);
        
        if ($request->has('label')) {
            $setting->label = $request->label;
        }
        
        if ($request->has('description')) {
            $setting->description = $request->description;
        }
        
        if ($request->has('is_public')) {
            $setting->is_public = $request->is_public;
        }

        $setting->save();

        return response()->json([
            'success' => true,
            'message' => 'Paramètre mis à jour avec succès',
            'data' => $setting
        ]);
    }

    /**
     * Create a new setting.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'key' => 'required|string|unique:app_settings,key|max:255',
            'value' => 'required',
            'type' => 'required|in:string,integer,float,boolean,json,date',
            'group' => 'required|string|max:255',
            'label' => 'required|string|max:255',
            'description' => 'nullable|string',
            'is_public' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $setting = new AppSetting($request->only([
            'key', 'type', 'group', 'label', 'description', 'is_public'
        ]));

        $setting->setTypedValue($request->value);
        $setting->save();

        return response()->json([
            'success' => true,
            'message' => 'Paramètre créé avec succès',
            'data' => $setting
        ], 201);
    }

    /**
     * Delete a setting.
     */
    public function destroy($id): JsonResponse
    {
        $setting = AppSetting::findOrFail($id);
        
        // Prevent deletion of critical settings
        $criticalSettings = [
            'free_trial_enabled',
            'free_trial_duration_months',
            'subscription_required_for_publishing',
            'payment_phone_number',
        ];

        if (in_array($setting->key, $criticalSettings)) {
            return response()->json([
                'success' => false,
                'message' => 'Ce paramètre critique ne peut pas être supprimé'
            ], 400);
        }

        $setting->delete();

        return response()->json([
            'success' => true,
            'message' => 'Paramètre supprimé avec succès'
        ]);
    }

    /**
     * Update multiple settings at once by key name.
     */
    public function updateBulk(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'settings' => 'required|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $updatedSettings = [];
            $settings = $request->input('settings', []);

            foreach ($settings as $key => $value) {
                // Find or create the setting
                $setting = AppSetting::where('key', $key)->first();

                if (!$setting) {
                    // Create new setting if it doesn't exist
                    $setting = new AppSetting();
                    $setting->key = $key;
                    $setting->group = $this->getSettingGroup($key);
                    $setting->label = $this->getSettingLabel($key);
                    $setting->type = $this->getSettingType($key, $value);
                    $setting->is_public = $this->isPublicSetting($key);
                }

                $setting->setTypedValue($value);
                $setting->save();
                $updatedSettings[] = $setting;
            }

            // Clear cache after updating settings
            AppSetting::clearCache();

            return response()->json([
                'success' => true,
                'message' => count($updatedSettings) . ' paramètres mis à jour avec succès',
                'data' => $updatedSettings
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la mise à jour des paramètres',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get the appropriate group for a setting key.
     */
    private function getSettingGroup(string $key): string
    {
        if (str_starts_with($key, 'free_trial_')) {
            return 'free_trial';
        }
        if (str_starts_with($key, 'payment_')) {
            return 'payment';
        }
        if (str_starts_with($key, 'subscription_')) {
            return 'subscription';
        }
        return 'general';
    }

    /**
     * Get the appropriate label for a setting key.
     */
    private function getSettingLabel(string $key): string
    {
        $labels = [
            'free_trial_duration_days' => 'Durée de l\'essai gratuit (jours)',
            'free_trial_start_date' => 'Date de début de l\'essai gratuit',
            'subscription_required_for_publishing' => 'Abonnement requis pour publier',
            'payment_bank_name' => 'Nom de la banque',
            'payment_account_number' => 'Numéro de compte',
            'payment_account_name' => 'Nom du compte',
            'payment_phone_number' => 'Numéro de téléphone',
            'payment_instructions' => 'Instructions de paiement',
        ];

        return $labels[$key] ?? ucfirst(str_replace('_', ' ', $key));
    }

    /**
     * Get the appropriate type for a setting based on key and value.
     */
    private function getSettingType(string $key, $value): string
    {
        if (is_bool($value) || $key === 'subscription_required_for_publishing') {
            return 'boolean';
        }
        if (is_int($value) || $key === 'free_trial_duration_days') {
            return 'integer';
        }
        if ($key === 'free_trial_start_date') {
            return 'date';
        }
        return 'string';
    }

    /**
     * Check if a setting should be public.
     */
    private function isPublicSetting(string $key): bool
    {
        $publicSettings = [
            'payment_bank_name',
            'payment_account_number',
            'payment_account_name',
            'payment_phone_number',
            'payment_instructions',
        ];

        return in_array($key, $publicSettings);
    }

    /**
     * Get public settings for frontend.
     */
    public function publicSettings(): JsonResponse
    {
        $settings = AppSetting::getPublicSettings();

        return response()->json([
            'success' => true,
            'data' => $settings
        ]);
    }
}
