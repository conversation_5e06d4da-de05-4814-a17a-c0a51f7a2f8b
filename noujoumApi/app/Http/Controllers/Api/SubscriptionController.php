<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SubscriptionPackage;
use App\Models\UserSubscription;
use App\Models\PaymentTransaction;
use App\Models\AppSetting;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class SubscriptionController extends Controller
{
    /**
     * Get all active subscription packages.
     */
    public function packages(): JsonResponse
    {
        $packages = SubscriptionPackage::active()
            ->ordered()
            ->get();

        return response()->json([
            'success' => true,
            'data' => $packages
        ]);
    }

    /**
     * Get user's current subscription status.
     */
    public function status(Request $request): JsonResponse
    {
        $user = $request->user();
        $subscription = $user->currentSubscription();
        $isGlobalFreeTrial = $user->isGlobalFreeTrialActive();

        // Determine subscription status text
        $subscriptionStatusText = 'Aucun abonnement';
        if ($isGlobalFreeTrial) {
            $subscriptionStatusText = 'Essai gratuit global actif';
        } elseif ($subscription && $subscription->isActive()) {
            $subscriptionStatusText = 'Abonnement actif';
        } elseif ($subscription && $subscription->isFreeTrial()) {
            $subscriptionStatusText = 'Essai gratuit';
        }

        $data = [
            'hasSubscription' => $subscription !== null || $isGlobalFreeTrial,
            'canPublish' => $user->canPublishApps(),
            'isFreeTrial' => $user->isInFreeTrial(),
            'isGlobalFreeTrial' => $isGlobalFreeTrial,
            'subscriptionStatus' => $subscriptionStatusText,
            'subscription' => $subscription,
        ];

        if ($subscription) {
            $data['daysRemaining'] = $subscription->days_remaining;
            $data['expiresAt'] = $subscription->expires_at;
        } elseif ($isGlobalFreeTrial) {
            // Add global free trial expiration info
            $endDate = AppSetting::get('free_trial_end_date');

            if ($endDate) {
                $end = \Carbon\Carbon::parse($endDate);
                $daysRemaining = now()->diffInDays($end, false);

                $data['daysRemaining'] = max(0, $daysRemaining);
                $data['expiresAt'] = $end;
                $data['globalFreeTrialEnd'] = $end;
            }
        }

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Get payment settings and information.
     */
    public function paymentInfo(): JsonResponse
    {
        $paymentInfo = [
            'phone_number' => AppSetting::get('payment_phone_number'),
            'bank_name' => AppSetting::get('payment_bank_name'),
            'account_number' => AppSetting::get('payment_account_number'),
            'instructions' => AppSetting::get('payment_instructions'),
        ];

        return response()->json([
            'success' => true,
            'data' => $paymentInfo
        ]);
    }

    /**
     * Create a payment transaction for a subscription package.
     */
    public function createPayment(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'package_id' => 'required|exists:subscription_packages,id',
            'transaction_image' => 'required|image|mimes:jpeg,png,jpg|max:5120', // 5MB max
            'user_notes' => 'nullable|string|max:1000',
            'paid_at' => 'required|date',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = $request->user();
            $package = SubscriptionPackage::findOrFail($request->package_id);

            // Upload transaction image
            $imagePath = $request->file('transaction_image')->store('transaction_images', 'public');
            $imageUrl = Storage::url($imagePath);

            // Create payment transaction
            $transaction = PaymentTransaction::create([
                'user_id' => $user->id,
                'package_id' => $package->id,
                'transaction_reference' => PaymentTransaction::generateReference(),
                'amount' => $package->price,
                'currency' => 'MRU',
                'status' => 'pending',
                'payment_method' => 'bank_transfer',
                'transaction_image_url' => $imageUrl,
                'user_notes' => $request->user_notes,
                'paid_at' => $request->paid_at,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Transaction créée avec succès. Elle sera examinée par notre équipe.',
                'data' => [
                    'transaction' => $transaction,
                    'reference' => $transaction->transaction_reference,
                ]
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la création de la transaction',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get user's payment transactions.
     */
    public function transactions(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $transactions = $user->paymentTransactions()
            ->with(['package', 'reviewer'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return response()->json([
            'success' => true,
            'data' => $transactions
        ]);
    }

    /**
     * Get a specific transaction.
     */
    public function transaction(Request $request, $id): JsonResponse
    {
        $user = $request->user();
        
        $transaction = $user->paymentTransactions()
            ->with(['package', 'reviewer', 'subscription'])
            ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $transaction
        ]);
    }

    /**
     * Cancel a pending transaction.
     */
    public function cancelTransaction(Request $request, $id): JsonResponse
    {
        $user = $request->user();
        
        $transaction = $user->paymentTransactions()
            ->where('status', 'pending')
            ->findOrFail($id);

        $transaction->update(['status' => 'cancelled']);

        return response()->json([
            'success' => true,
            'message' => 'Transaction annulée avec succès'
        ]);
    }

    /**
     * Get app settings related to subscriptions.
     */
    public function settings(): JsonResponse
    {
        $settings = AppSetting::public()
            ->whereIn('group', ['subscription', 'payment', 'general'])
            ->get()
            ->pluck('typed_value', 'key');

        return response()->json([
            'success' => true,
            'data' => $settings
        ]);
    }
}
