<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\AppSetting;

class CheckSubscription
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if subscription is required for publishing
        $subscriptionRequired = AppSetting::get('subscription_required_for_publishing', true);
        
        if (!$subscriptionRequired) {
            return $next($request);
        }

        $user = $request->user();
        
        // Allow admins to bypass subscription check
        if ($user && $user->isAdmin()) {
            return $next($request);
        }

        // Check if user can publish apps (has active subscription or in free trial)
        if (!$user || !$user->canPublishApps()) {
            return response()->json([
                'success' => false,
                'message' => 'Un abonnement actif est requis pour publier des applications',
                'error_code' => 'SUBSCRIPTION_REQUIRED',
                'data' => [
                    'subscription_status' => $user ? $user->subscription_status : 'Aucun abonnement',
                    'can_publish' => false,
                ]
            ], 403);
        }

        return $next($request);
    }
}
