<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\UserSubscription;
use App\Models\AppSetting;
use Carbon\Carbon;

class UserSubscriptionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get free trial settings
        $freeTrialEnabled = AppSetting::get('free_trial_enabled', true);
        $freeTrialDurationMonths = AppSetting::get('free_trial_duration_months', 6);
        $freeTrialStartDate = AppSetting::get('free_trial_start_date', now()->toDateString());
        $freeTrialEndDate = AppSetting::get('free_trial_end_date', now()->addMonths(6)->toDateString());

        if (!$freeTrialEnabled) {
            $this->command->info('Free trial is disabled. Skipping user subscription seeding.');
            return;
        }

        // Get all existing users (excluding admins)
        $users = User::where('role', 'user')->get();

        $startDate = Carbon::parse($freeTrialStartDate);
        $endDate = Carbon::parse($freeTrialEndDate);

        foreach ($users as $user) {
            // Check if user already has a subscription
            if ($user->subscriptions()->exists()) {
                continue;
            }

            // Create free trial subscription for each user
            UserSubscription::create([
                'user_id' => $user->id,
                'package_id' => null, // No package for free trial
                'status' => 'free_trial',
                'starts_at' => $startDate,
                'expires_at' => $endDate,
                'free_trial_ends_at' => $endDate,
                'is_free_trial' => true,
                'amount_paid' => null,
                'currency' => 'MRU',
                'notes' => 'Période d\'essai gratuit de ' . $freeTrialDurationMonths . ' mois',
            ]);

            $this->command->info("Created free trial subscription for user: {$user->name} ({$user->email})");
        }

        $this->command->info("Created free trial subscriptions for {$users->count()} users.");
    }
}
