<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\SubscriptionPackage;
use App\Models\AppSetting;

class SubscriptionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create default subscription packages
        $packages = [
            [
                'name' => '1 Mois',
                'description' => 'Abonnement mensuel pour publier vos applications',
                'duration_months' => 1,
                'price' => 5000, // 5000 MRU
                'original_price' => null,
                'is_active' => true,
                'sort_order' => 1,
                'features' => [
                    'Publication illimitée d\'applications',
                    'Support technique',
                    'Statistiques de base',
                ],
                'badge' => null,
            ],
            [
                'name' => '3 Mois',
                'description' => 'Abonnement trimestriel avec réduction',
                'duration_months' => 3,
                'price' => 12000, // 12000 MRU (instead of 15000)
                'original_price' => 15000,
                'is_active' => true,
                'sort_order' => 2,
                'features' => [
                    'Publication illimitée d\'applications',
                    'Support technique prioritaire',
                    'Statistiques avancées',
                    '20% de réduction',
                ],
                'badge' => 'Populaire',
            ],
            [
                'name' => '1 Année',
                'description' => 'Abonnement annuel avec la meilleure réduction',
                'duration_months' => 12,
                'price' => 40000, // 40000 MRU (instead of 60000)
                'original_price' => 60000,
                'is_active' => true,
                'sort_order' => 3,
                'features' => [
                    'Publication illimitée d\'applications',
                    'Support technique VIP',
                    'Statistiques complètes',
                    'Promotion d\'applications',
                    '33% de réduction',
                ],
                'badge' => 'Meilleure valeur',
            ],
        ];

        foreach ($packages as $packageData) {
            SubscriptionPackage::create($packageData);
        }

        // Create default app settings
        $settings = [
            // Free trial settings
            [
                'key' => 'free_trial_enabled',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'subscription',
                'label' => 'Essai gratuit activé',
                'description' => 'Activer ou désactiver la période d\'essai gratuit',
                'is_public' => true,
            ],
            [
                'key' => 'free_trial_duration_months',
                'value' => '6',
                'type' => 'integer',
                'group' => 'subscription',
                'label' => 'Durée essai gratuit (mois)',
                'description' => 'Durée de la période d\'essai gratuit en mois',
                'is_public' => true,
            ],
            [
                'key' => 'free_trial_start_date',
                'value' => now()->toDateString(),
                'type' => 'date',
                'group' => 'subscription',
                'label' => 'Date début essai gratuit',
                'description' => 'Date de début de la période d\'essai gratuit',
                'is_public' => true,
            ],
            [
                'key' => 'free_trial_end_date',
                'value' => now()->addMonths(6)->toDateString(),
                'type' => 'date',
                'group' => 'subscription',
                'label' => 'Date fin essai gratuit',
                'description' => 'Date de fin de la période d\'essai gratuit',
                'is_public' => true,
            ],
            
            // Payment settings
            [
                'key' => 'payment_phone_number',
                'value' => '+222 45 67 89 00',
                'type' => 'string',
                'group' => 'payment',
                'label' => 'Numéro de téléphone pour paiement',
                'description' => 'Numéro de téléphone pour les virements bancaires',
                'is_public' => true,
            ],
            [
                'key' => 'payment_bank_name',
                'value' => 'Banque Mauritanienne',
                'type' => 'string',
                'group' => 'payment',
                'label' => 'Nom de la banque',
                'description' => 'Nom de la banque pour les virements',
                'is_public' => true,
            ],
            [
                'key' => 'payment_account_number',
                'value' => '*********',
                'type' => 'string',
                'group' => 'payment',
                'label' => 'Numéro de compte bancaire',
                'description' => 'Numéro de compte pour les virements',
                'is_public' => true,
            ],
            [
                'key' => 'payment_instructions',
                'value' => 'Effectuez le virement bancaire et envoyez la preuve de transaction avec votre nom d\'utilisateur.',
                'type' => 'string',
                'group' => 'payment',
                'label' => 'Instructions de paiement',
                'description' => 'Instructions pour les utilisateurs concernant le paiement',
                'is_public' => true,
            ],
            
            // General settings
            [
                'key' => 'subscription_required_for_publishing',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'general',
                'label' => 'Abonnement requis pour publier',
                'description' => 'Les utilisateurs doivent avoir un abonnement actif pour publier des applications',
                'is_public' => true,
            ],
        ];

        foreach ($settings as $settingData) {
            AppSetting::create($settingData);
        }
    }
}
