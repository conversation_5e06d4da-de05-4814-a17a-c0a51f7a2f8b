<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('app_settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique(); // e.g., 'free_trial_duration', 'payment_phone_number'
            $table->text('value'); // JSON or string value
            $table->string('type')->default('string'); // string, integer, boolean, json, date
            $table->string('group')->default('general'); // general, subscription, payment, etc.
            $table->string('label'); // Human readable label
            $table->text('description')->nullable(); // Description for admin
            $table->boolean('is_public')->default(false); // Can be accessed by frontend
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('app_settings');
    }
};
