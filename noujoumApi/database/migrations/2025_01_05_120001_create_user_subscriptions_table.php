<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_subscriptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('package_id')->nullable()->constrained('subscription_packages')->onDelete('set null');
            $table->enum('status', ['free_trial', 'active', 'expired', 'cancelled'])->default('free_trial');
            $table->datetime('starts_at');
            $table->datetime('expires_at');
            $table->datetime('free_trial_ends_at')->nullable(); // For the 6-month free trial
            $table->boolean('is_free_trial')->default(true);
            $table->decimal('amount_paid', 10, 2)->nullable();
            $table->string('currency', 3)->default('MRU'); // Mauritanian Ouguiya
            $table->text('notes')->nullable();
            $table->timestamps();
            
            // Indexes
            $table->index(['user_id', 'status']);
            $table->index('expires_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_subscriptions');
    }
};
