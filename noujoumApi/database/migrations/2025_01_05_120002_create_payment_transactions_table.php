<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('subscription_id')->nullable()->constrained('user_subscriptions')->onDelete('set null');
            $table->foreignId('package_id')->constrained('subscription_packages')->onDelete('cascade');
            $table->string('transaction_reference')->unique(); // Generated reference
            $table->decimal('amount', 10, 2);
            $table->string('currency', 3)->default('MRU');
            $table->enum('status', ['pending', 'approved', 'rejected', 'cancelled'])->default('pending');
            $table->string('payment_method')->default('bank_transfer'); // bank_transfer, mobile_money, etc.
            $table->string('transaction_image_url')->nullable(); // User uploaded transaction proof
            $table->text('user_notes')->nullable(); // User can add notes about the transaction
            $table->text('admin_notes')->nullable(); // Admin notes for approval/rejection
            $table->foreignId('reviewed_by')->nullable()->constrained('users')->onDelete('set null'); // Admin who reviewed
            $table->datetime('reviewed_at')->nullable();
            $table->datetime('paid_at')->nullable(); // When user claims to have paid
            $table->timestamps();
            
            // Indexes
            $table->index(['user_id', 'status']);
            $table->index('status');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_transactions');
    }
};
