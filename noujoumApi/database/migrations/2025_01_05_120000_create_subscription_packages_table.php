<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_packages', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // e.g., "1 Month", "3 Months", "1 Year"
            $table->text('description')->nullable();
            $table->integer('duration_months'); // 1, 3, 12
            $table->decimal('price', 10, 2); // Price in MRU (Mauritanian Ouguiya)
            $table->decimal('original_price', 10, 2)->nullable(); // For showing discounts
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->json('features')->nullable(); // JSON array of features
            $table->string('badge')->nullable(); // e.g., "Most Popular", "Best Value"
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_packages');
    }
};
