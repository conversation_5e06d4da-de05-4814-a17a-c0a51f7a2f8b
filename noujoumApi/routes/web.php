<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\AdminAuthController;
use App\Http\Controllers\Admin\AdminDashboardController;

Route::get('/', function () {
    return view('welcome');
});

// Admin Routes
Route::prefix('admin')->group(function () {
    // Authentication routes
    Route::get('/login', [AdminAuthController::class, 'showLoginForm'])->name('admin.login');
    Route::post('/login', [AdminAuthController::class, 'login']);
    Route::post('/logout', [AdminAuthController::class, 'logout'])->name('admin.logout');
    
    // Protected admin routes
    Route::middleware(['auth'])->group(function () {
        Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('admin.dashboard');
        
        // User management
        Route::get('/users', [AdminDashboardController::class, 'users'])->name('admin.users.index');
        Route::get('/users/{id}', [AdminDashboardController::class, 'showUser'])->name('admin.users.show');
        Route::post('/users/{id}/verify', [AdminDashboardController::class, 'verifyUser'])->name('admin.users.verify');
        Route::post('/users/{id}/unverify', [AdminDashboardController::class, 'unverifyUser'])->name('admin.users.unverify');
        
        // App management
        Route::get('/apps', [AdminDashboardController::class, 'apps'])->name('admin.apps');
        Route::get('/apps/{id}', [AdminDashboardController::class, 'showApp'])->name('admin.apps.show');
        Route::post('/apps/{id}/approve', [AdminDashboardController::class, 'approveApp'])->name('admin.apps.approve');
        Route::post('/apps/{id}/reject', [AdminDashboardController::class, 'rejectApp'])->name('admin.apps.reject');
        Route::post('/apps/{id}/feature', [AdminDashboardController::class, 'featureApp'])->name('admin.apps.feature');
        Route::post('/apps/{id}/unfeature', [AdminDashboardController::class, 'unfeatureApp'])->name('admin.apps.unfeature');
        Route::delete('/apps/{id}', [AdminDashboardController::class, 'deleteApp'])->name('admin.apps.delete');

        // Subscription management
        Route::get('/subscriptions', [AdminDashboardController::class, 'subscriptions'])->name('admin.subscriptions');
        Route::get('/subscriptions/packages', [AdminDashboardController::class, 'subscriptionPackages'])->name('admin.subscriptions.packages');
        Route::get('/subscriptions/transactions', [AdminDashboardController::class, 'subscriptionTransactions'])->name('admin.subscriptions.transactions');
        Route::get('/settings', [AdminDashboardController::class, 'settings'])->name('admin.settings');
        Route::post('/settings/bulk-update', [AdminDashboardController::class, 'updateSettings'])->name('admin.settings.bulk-update');
    });
});
