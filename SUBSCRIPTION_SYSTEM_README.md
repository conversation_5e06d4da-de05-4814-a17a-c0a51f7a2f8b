# Noujoum Store Subscription System

## 🎯 Overview

This document describes the comprehensive subscription system implemented for Noujoum Store, allowing users to subscribe to publish applications with a 6-month free trial period and manual payment validation.

## 🏗️ Architecture

### Backend (Laravel 11)

#### Database Schema
- **subscription_packages**: Subscription plans (1 month, 3 months, 1 year)
- **user_subscriptions**: User subscription status and free trial tracking
- **payment_transactions**: Payment submissions with manual approval workflow
- **app_settings**: Parametrable system settings

#### Models
- `SubscriptionPackage`: Package management with discount calculations
- `UserSubscription`: Subscription lifecycle management
- `PaymentTransaction`: Payment workflow with approval/rejection
- `AppSetting`: Dynamic configuration with caching
- `User`: Extended with subscription methods

#### Controllers
- `Api/SubscriptionController`: User-facing subscription API
- `Admin/SubscriptionController`: Admin payment approval/rejection
- `Admin/SettingsController`: Settings management interface

#### Middleware
- `CheckSubscription`: Validates subscription status for app publishing

### Frontend (Flutter)

#### Models
- `SubscriptionPackage`: Package data structure
- `UserSubscription`: User subscription status
- `PaymentTransaction`: Payment transaction details

#### Services
- `SubscriptionService`: API communication for subscription operations

#### Screens
- `SubscriptionPackagesScreen`: Display available packages
- `PaymentScreen`: Upload transaction image and submit payment
- `PaymentHistoryScreen`: View payment history and status

#### Widgets
- `SubscriptionStatusWidget`: Display subscription status in user profile

## 🚀 Features Implemented

### ✅ Backend Features

1. **Subscription Packages Management**
   - Create, update, delete subscription packages
   - Pricing with discount support
   - Package features and badges
   - Sort ordering

2. **Free Trial System**
   - Parametrable 6-month free trial
   - Configurable start and end dates
   - Automatic assignment to new users
   - Seamless transition to paid subscriptions

3. **Manual Payment Validation**
   - Users upload transaction images
   - Admin approval/rejection workflow
   - Payment reference generation
   - Status tracking and notifications

4. **Subscription Management**
   - Active subscription checking
   - Expiration handling
   - Publishing permission control
   - Subscription extension

5. **Settings Management**
   - Parametrable free trial settings
   - Payment information configuration
   - System-wide subscription controls
   - Cached settings for performance

6. **API Endpoints**
   - Public: packages, payment info, settings
   - User: status, payment submission, transaction history
   - Admin: stats, package CRUD, payment approval, settings

### ✅ Frontend Features

1. **Subscription Package Selection**
   - Beautiful package cards with pricing
   - Discount badges and popular indicators
   - Feature lists and duration display
   - Golden color scheme integration

2. **Payment Submission**
   - Image picker for transaction receipts
   - Payment date selection
   - Optional notes field
   - Bank information display

3. **Subscription Status Display**
   - Current subscription information
   - Days remaining calculation
   - Free trial vs paid status
   - Action buttons (upgrade, extend, renew)

4. **Payment History**
   - Transaction list with status
   - Detailed transaction information
   - Cancel pending transactions
   - Admin notes display

5. **User Dashboard Integration**
   - Subscription status widget
   - Payment history access
   - Publishing permission checks

### ✅ Admin Interface

1. **Web Dashboard**
   - Subscription statistics
   - Recent transactions overview
   - Quick approval/rejection actions
   - Golden color scheme

2. **Settings Management**
   - Free trial configuration
   - Payment information setup
   - System toggles
   - Bulk settings update

## 🔧 Configuration

### Environment Variables
```env
# Add to .env file
SUBSCRIPTION_REQUIRED=true
FREE_TRIAL_ENABLED=true
FREE_TRIAL_DURATION_MONTHS=6
```

### Default Settings (Seeded)
- **Free Trial**: 6 months enabled
- **Packages**: 1 month (5000 MRU), 3 months (12000 MRU, 20% off), 1 year (40000 MRU, 33% off)
- **Payment**: Bank transfer with Mauritanian phone number

## 📱 User Flow

1. **New User Registration**
   - Automatic 6-month free trial assignment
   - Can publish apps immediately
   - Subscription status visible in profile

2. **Package Selection**
   - Browse available packages
   - Compare features and pricing
   - Select preferred duration

3. **Payment Process**
   - View payment instructions
   - Make bank transfer
   - Upload transaction receipt
   - Add optional notes

4. **Admin Review**
   - Admin reviews transaction image
   - Approves or rejects payment
   - Adds review notes if needed

5. **Subscription Activation**
   - Approved payments activate subscription
   - User can continue publishing
   - Subscription extends current period

## 🛠️ Installation & Setup

### 1. Run Migrations
```bash
cd noujoumApi
php artisan migrate
```

### 2. Seed Default Data
```bash
php artisan db:seed --class=SubscriptionSeeder
php artisan db:seed --class=UserSubscriptionSeeder
```

### 3. Configure Settings
- Access `/admin/settings` in web browser
- Configure free trial dates
- Set payment information
- Adjust system settings

### 4. Flutter Dependencies
The required dependencies are already in `pubspec.yaml`:
- `image_picker: ^1.1.2` - For transaction image upload
- `http: ^1.1.0` - For API communication

## 🎨 Design Integration

### Color Scheme
- **Primary Gold**: #FFB000 (main subscription actions)
- **Mauritanian Green**: #228B22 (active subscriptions)
- **Primary Orange**: #E67E22 (free trial, warnings)
- **Rich Red**: #C0392B (expired, rejected)

### UI Components
- Consistent with existing app design
- Material Design principles
- Responsive layouts
- Loading states and error handling

## 🔐 Security Features

1. **Authentication Required**
   - All subscription operations require login
   - Sanctum token authentication

2. **Input Validation**
   - Server-side validation for all inputs
   - File type and size restrictions
   - XSS protection

3. **Admin Authorization**
   - Admin-only access to approval functions
   - Role-based permissions

4. **Data Integrity**
   - Transaction reference uniqueness
   - Subscription status consistency
   - Audit trail for all changes

## 📊 Monitoring & Analytics

### Available Metrics
- Total users count
- Active subscriptions
- Pending payments
- Free trial users
- Revenue tracking
- Conversion rates

### Admin Dashboard
- Real-time statistics
- Recent transaction list
- Quick action buttons
- Status indicators

## 🚀 Next Steps

### Immediate Actions Required
1. **Run migrations and seeders** (user needs to do this)
2. **Configure payment settings** via admin interface
3. **Test payment flow** with sample transactions
4. **Train admin users** on approval process

### Future Enhancements
1. **Automated Payment Integration**
   - Mobile money API integration
   - Bank API connections
   - Automatic payment verification

2. **Advanced Analytics**
   - Revenue dashboards
   - User behavior tracking
   - Subscription lifecycle analysis

3. **Notification System**
   - Email notifications for payments
   - SMS alerts for approvals
   - Push notifications in app

4. **Multi-Currency Support**
   - USD, EUR pricing options
   - Exchange rate handling
   - Regional pricing

## 🐛 Troubleshooting

### Common Issues
1. **Migration Errors**: Ensure database connection is configured
2. **Image Upload Fails**: Check storage permissions and disk space
3. **API Errors**: Verify Sanctum configuration and CORS settings
4. **Settings Not Loading**: Clear cache with `php artisan cache:clear`

### Debug Commands
```bash
# Check subscription status
php artisan tinker
>>> App\Models\User::find(1)->canPublishApps()

# Clear settings cache
php artisan cache:clear

# View recent transactions
>>> App\Models\PaymentTransaction::latest()->take(5)->get()
```

## 📞 Support

For technical support or questions about the subscription system:
1. Check this documentation first
2. Review Laravel logs in `storage/logs/`
3. Test API endpoints with Postman
4. Contact development team with specific error messages

---

**Status**: ✅ Implementation Complete - Ready for Testing
**Version**: 1.0.0
**Last Updated**: January 2025
